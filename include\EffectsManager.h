#pragma once

#include <Arduino.h>
#include <ArduinoJson.h>
#include <ESPAsyncWebServer.h>
#include <LittleFS.h>
#include <FastLED.h>
#include <vector>
#include "Config.h"
#include "LightEffect.h"

// 前向声明
class SegmentsManager;
class ScenesManager;
class PrinterManager;

/**
 * 分段方向配置结构
 */
struct SegmentDirection {
    String segmentId;     // 分段ID
    uint8_t direction;    // 方向 (0=正向, 1=反向)
    
    // 构造函数
    SegmentDirection() : direction(0) {}
    SegmentDirection(const String& id, uint8_t dir) : segmentId(id), direction(dir) {}
    
    // 转换为JSON对象
    void toJson(JsonObject obj) const {
        obj["segmentId"] = segmentId;
        obj["direction"] = direction;
    }
    
    // 从JSON对象创建
    static SegmentDirection fromJson(const JsonObject& obj) {
        SegmentDirection sd;
        sd.segmentId = obj["segmentId"].as<String>();
        sd.direction = obj["direction"].as<uint8_t>();
        return sd;
    }
};

/**
 * 灯效预设数据结构
 */
struct EffectPreset {
    String id;                              // 预设唯一标识符
    String name;                            // 预设名称
    EffectType effectType;                  // 灯效类型
    std::vector<SegmentDirection> segments; // 应用的分段及方向配置
    EffectParams params;                    // 灯效参数
    bool enabled;                           // 是否启用
    unsigned long createdTime;              // 创建时间
    
    // 构造函数
    EffectPreset() : effectType(EffectType::BREATHING), enabled(true), createdTime(0) {}
    EffectPreset(const String& presetId, const String& presetName, EffectType type)
        : id(presetId), name(presetName), effectType(type), enabled(true), createdTime(millis()) {}
    
    // 转换为JSON对象
    void toJson(JsonObject obj) const {
        obj["id"] = id;
        obj["name"] = name;
        obj["effectType"] = static_cast<int>(effectType);
        obj["effectTypeName"] = LightEffect::getEffectName(effectType);
        obj["enabled"] = enabled;
        obj["createdTime"] = createdTime;
        
        // 分段配置
        JsonArray segmentsArray = obj.createNestedArray("segments");
        for (const auto& segment : segments) {
            JsonObject segmentObj = segmentsArray.createNestedObject();
            segment.toJson(segmentObj);
        }
        
        // 参数配置
        JsonObject paramsObj = obj.createNestedObject("params");
        paramsObj["brightness"] = params.brightness;
        paramsObj["speed"] = params.speed;

        // 保存为颜色数组格式
        JsonArray colorsArray = paramsObj.createNestedArray("colors");
        for (const auto& color : params.colors) {
            JsonObject colorObj = colorsArray.createNestedObject();
            colorObj["r"] = color.r;
            colorObj["g"] = color.g;
            colorObj["b"] = color.b;
        }

        paramsObj["direction"] = params.direction;
        paramsObj["intensity"] = params.intensity;
    }
    
    // 从JSON对象创建预设
    static EffectPreset fromJson(const JsonObject& obj) {
        EffectPreset preset;
        preset.id = obj["id"].as<String>();
        preset.name = obj["name"].as<String>();
        preset.effectType = static_cast<EffectType>(obj["effectType"].as<int>());
        preset.enabled = obj["enabled"].as<bool>();
        preset.createdTime = obj["createdTime"].as<unsigned long>();
        
        // 解析分段配置
        JsonArray segmentsArray = obj["segments"];
        for (JsonVariant segmentVar : segmentsArray) {
            JsonObject segmentObj = segmentVar.as<JsonObject>();
            preset.segments.push_back(SegmentDirection::fromJson(segmentObj));
        }
        
        // 解析参数配置
        JsonObject paramsObj = obj["params"];
        preset.params.brightness = paramsObj["brightness"].as<uint8_t>();
        preset.params.speed = paramsObj["speed"].as<uint8_t>();

        // 解析颜色数组（支持新格式和旧格式兼容）
        preset.params.colors.clear();
        if (paramsObj.containsKey("colors")) {
            // 新的多颜色格式
            JsonArray colorsArray = paramsObj["colors"];
            for (JsonVariant colorVar : colorsArray) {
                JsonObject colorObj = colorVar.as<JsonObject>();
                preset.params.colors.push_back(CRGB(
                    colorObj["r"].as<uint8_t>(),
                    colorObj["g"].as<uint8_t>(),
                    colorObj["b"].as<uint8_t>()
                ));
            }
        } else if (paramsObj.containsKey("color")) {
            // 兼容旧的单色格式
            JsonObject colorObj = paramsObj["color"];
            preset.params.colors.push_back(CRGB(
                colorObj["r"].as<uint8_t>(),
                colorObj["g"].as<uint8_t>(),
                colorObj["b"].as<uint8_t>()
            ));
        }

        // 如果没有颜色数据，设置默认红色
        if (preset.params.colors.empty()) {
            preset.params.colors.push_back(CRGB::Red);
        }

        preset.params.direction = paramsObj["direction"].as<uint8_t>();
        preset.params.intensity = paramsObj["intensity"].as<uint8_t>();
        
        return preset;
    }
};

/**
 * 灯效管理器
 * 
 * 负责灯效预设的创建、编辑、删除和持久化存储
 * 提供REST API接口供前端调用
 * 支持灯效的执行和渲染
 */
class EffectsManager {
public:
    /**
     * 构造函数
     */
    EffectsManager();
    
    /**
     * 析构函数
     */
    ~EffectsManager();
    
    /**
     * 初始化灯效管理器
     * 从文件系统加载已保存的灯效预设
     */
    void begin();
    
    /**
     * 主循环处理函数
     * 处理灯效渲染和更新
     * 应在主循环中定期调用
     */
    void loop();
    
    /**
     * 注册API端点到WebServer
     * 
     * @param server AsyncWebServer实例引用
     */
    void registerAPI(AsyncWebServer& server);
    
    /**
     * 设置SegmentsManager引用
     * 用于获取分段信息
     *
     * @param segmentsManager SegmentsManager实例指针
     */
    void setSegmentsManager(SegmentsManager* segmentsManager);

    /**
     * 设置ScenesManager引用
     * 用于控制场景功能的启用和禁用
     *
     * @param scenesManager ScenesManager实例指针
     */
    void setScenesManager(ScenesManager* scenesManager);

    /**
     * 设置PrinterManager引用
     * 用于获取实时温度数据
     *
     * @param printerManager PrinterManager实例指针
     */
    void setPrinterManager(PrinterManager* printerManager);
    
    /**
     * 获取所有灯效预设
     * 
     * @return 灯效预设列表的引用
     */
    const std::vector<EffectPreset>& getPresets() const;
    
    /**
     * 根据ID获取灯效预设
     * 
     * @param id 预设ID
     * @return 预设指针，如果不存在返回nullptr
     */
    const EffectPreset* getPresetById(const String& id) const;
    
    /**
     * 添加新灯效预设
     * 
     * @param preset 要添加的预设
     * @return 成功返回true，失败返回false
     */
    bool addPreset(const EffectPreset& preset);
    
    /**
     * 更新灯效预设
     * 
     * @param id 要更新的预设ID
     * @param preset 新的预设数据
     * @return 成功返回true，失败返回false
     */
    bool updatePreset(const String& id, const EffectPreset& preset);
    
    /**
     * 删除灯效预设
     * 
     * @param id 要删除的预设ID
     * @return 成功返回true，失败返回false
     */
    bool deletePreset(const String& id);
    
    /**
     * 执行指定的灯效预设
     *
     * @param presetId 预设ID
     * @return 成功返回true，失败返回false
     */
    bool executePreset(const String& presetId);

    /**
     * 停止当前正在执行的灯效
     */
    void stopCurrentEffect();
    
    /**
     * 获取当前正在执行的灯效预设ID
     * 
     * @return 当前执行的预设ID，如果没有返回空字符串
     */
    String getCurrentPresetId() const;
    
    /**
     * 检查灯效是否正在运行
     *
     * @return 是否正在运行
     */
    bool isEffectRunning() const;

    /**
     * 启用预览模式
     * 暂停场景自动化，关闭所有LED，等待预览指令
     *
     * @return 成功返回true，失败返回false
     */
    bool enablePreviewMode();

    /**
     * 禁用预览模式
     * 恢复场景自动化，重新激活当前状态对应的场景
     *
     * @return 成功返回true，失败返回false
     */
    bool disablePreviewMode();

    /**
     * 检查是否在预览模式
     *
     * @return 预览模式返回true，正常模式返回false
     */
    bool isPreviewMode() const;

    /**
     * 预览指定的灯效预设
     * 仅在预览模式下有效，立即渲染预览效果到LED
     *
     * @param preset 要预览的预设
     * @return 成功返回true，失败返回false
     */
    bool previewPreset(const EffectPreset& preset);

    /**
     * 停止当前预览
     * 关闭所有LED，清除预览数据
     *
     * @return 成功返回true，失败返回false
     */
    bool stopPreview();


    /**
     * 生成唯一的预设ID
     */
    String generatePresetId() const;
    
    /**
     * 验证预设配置是否有效
     * 
     * @param preset 要验证的预设
     * @param excludeId 验证时排除的预设ID（用于更新时）
     * @return 有效返回true，无效返回false
     */
    bool validatePreset(const EffectPreset& preset, const String& excludeId = "") const;

private:
    SegmentsManager* segmentsManager;       // SegmentsManager指针
    ScenesManager* scenesManager;           // ScenesManager指针
    PrinterManager* printerManager;         // PrinterManager指针
    std::vector<EffectPreset> presets;      // 预设列表
    const String configFile = "/effects.json"; // 配置文件路径

    // LED控制相关
    CRGB* leds;                            // LED数组指针
    bool ledsInitialized;                  // LED是否已初始化

    // 当前执行状态
    String currentPresetId;                // 当前执行的预设ID
    bool effectRunning;                    // 是否正在运行灯效

    // 预览模式相关
    bool previewMode;                      // 预览模式标志
    String previewPresetId;                // 当前预览的预设ID
    EffectPreset previewPresetData;        // 临时预览预设数据
    unsigned long lastPreviewTime;         // 上次预览渲染时间
    
    /**
     * 初始化LED控制
     */
    void initializeLEDs();
    
    /**
     * 保存预设配置到文件
     */
    bool saveToFile();
    
    /**
     * 从文件加载预设配置
     */
    bool loadFromFile();
    
    /**
     * 渲染当前灯效
     */
    void renderCurrentEffect();

    /**
     * 渲染预览灯效
     * 使用预览数据渲染灯效到LED
     */
    void renderPreviewEffect();

    /**
     * 通用灯效渲染方法
     * 统一的渲染逻辑，供正常模式和预览模式使用
     *
     * @param preset 要渲染的灯效预设
     * @param isPreview 是否为预览模式
     * @param currentTime 当前时间（毫秒）
     */
    void renderEffectInternal(const EffectPreset& preset, bool isPreview, unsigned long currentTime);

    /**
     * 灯效渲染分发器
     * 根据灯效类型调用对应的渲染函数
     */
    void dispatchEffectRender(EffectType effectType, int startLed, int endLed,
                            const EffectParams& params, unsigned long currentTime, bool isPreview);

    /**
     * 统一的分段范围获取方法
     * 正常模式和预览模式都使用相同的逻辑
     */
    bool getSegmentRangeForRender(const String& segmentId, int& startLed, int& endLed, bool isPreview);
    
    /**
     * 根据分段ID获取分段信息
     * 
     * @param segmentId 分段ID
     * @param startLed 输出起始LED位置
     * @param endLed 输出结束LED位置
     * @return 是否找到分段
     */
    bool getSegmentRange(const String& segmentId, int& startLed, int& endLed) const;
    
    /**
     * API处理函数
     */
    void handleGetPresets(AsyncWebServerRequest* request);
    void handleCreatePreset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleUpdatePreset(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleDeletePreset(AsyncWebServerRequest* request);
    void handleStopEffect(AsyncWebServerRequest* request);
    void handleGetStats(AsyncWebServerRequest* request);
    void handleGetAvailableEffects(AsyncWebServerRequest* request);

    /**
     * 预览模式API处理函数
     */
    void handleEnablePreview(AsyncWebServerRequest* request);
    void handleDisablePreview(AsyncWebServerRequest* request);
    void handlePreviewEffect(AsyncWebServerRequest* request, uint8_t* data, size_t len, size_t index, size_t total);
    void handleStopPreview(AsyncWebServerRequest* request);
    void handleGetPreviewStatus(AsyncWebServerRequest* request);
    
    /**
     * 工具函数
     */
    void sendJsonResponse(AsyncWebServerRequest* request, int code, const String& message, const JsonDocument* data = nullptr);
    void sendErrorResponse(AsyncWebServerRequest* request, int code, const String& message);
};
